"""
Multi-Material Job State Models
===============================

This module defines the data models for managing multi-material print jobs
with 3-drum coordination. It provides state management for job lifecycle,
drum coordination, and layer progression tracking.

Based on the Stage 2 requirements from the implementation documentation.
"""

import uuid
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Any
from services.cli_parser import CliLayer


class JobStatus(Enum):
    """Multi-material job status enumeration"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    WAITING_FOR_PRINT_START = "waiting_for_print_start"
    RECOATER_ACTIVE = "recoater_active"
    WAITING_FOR_DEPOSITION = "waiting_for_deposition"
    LAYER_COMPLETE = "layer_complete"
    JOB_COMPLETE = "job_complete"
    ERROR = "error"
    CANCELLED = "cancelled"
    RUNNING = "running"


@dataclass
class LayerData:
    """Individual layer information for multi-material printing"""
    layer_number: int
    cli_data: bytes
    is_empty: bool = False
    upload_time: Optional[float] = None
    completion_time: Optional[float] = None


@dataclass
class DrumState:
    """State tracking for individual drums in multi-material operation"""
    drum_id: int
    status: str = "idle"
    ready: bool = False
    uploaded: bool = False
    current_layer: int = 0
    total_layers: int = 0
    error_message: str = ""
    file_id: Optional[str] = None
    last_update_time: float = field(default_factory=time.time)

    def reset(self):
        """Reset drum state for new job"""
        self.status = "idle"
        self.ready = False
        self.uploaded = False
        self.current_layer = 0
        self.error_message = ""
        self.last_update_time = time.time()


@dataclass
class MultiMaterialJobState:
    """Complete state management for multi-material print jobs with 3 drums"""

    # Job identification
    job_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    file_ids: Dict[int, str] = field(default_factory=dict)  # drum_id -> file_id mapping

    # Job scope and progress
    total_layers: int = 0
    current_layer: int = 0
    remaining_layers: Dict[int, List[LayerData]] = field(default_factory=dict)  # drum_id -> layers
    header_lines: Dict[int, List[str]] = field(default_factory=dict)  # drum_id -> CLI header lines

    # Job status
    is_active: bool = False
    status: JobStatus = JobStatus.IDLE

    # Timing information
    start_time: Optional[float] = None
    last_layer_time: Optional[float] = None
    estimated_completion: Optional[float] = None

    # Process state tracking
    waiting_for_print_start: bool = False
    waiting_for_layer_complete: bool = False

    # Error handling
    error_message: str = ""
    retry_count: int = 0

    # Drum coordination (3 drums: 0, 1, 2)
    drums: Dict[int, DrumState] = field(default_factory=lambda: {
        0: DrumState(drum_id=0),
        1: DrumState(drum_id=1),
        2: DrumState(drum_id=2)
    })

    def get_progress_percentage(self) -> float:
        """Calculate job progress as percentage"""
        if self.total_layers == 0:
            return 0.0
        # Progress is based on completed layers (current_layer - 1)
        completed_layers = max(0, self.current_layer - 1)
        return (completed_layers / self.total_layers) * 100.0