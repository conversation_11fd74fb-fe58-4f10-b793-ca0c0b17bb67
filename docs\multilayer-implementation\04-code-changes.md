# Code Changes: Detailed Implementation Guide

## Overview

This document provides detailed code changes and implementations for the multi-layer print job system. Each section includes complete code examples, file modifications, and integration instructions.

## File Structure Changes

### New Files to Create
```
backend/
├── app/
│   ├── config/
│   │   └── opcua_config.py          # OPC UA server configuration
│   ├── models/
│   │   └── multilayer_job.py        # Job state models
│   ├── services/
│   │   ├── opcua_server.py          # OPC UA server management
│   │   ├── coordination_engine.py   # Simplified coordination logic
│   │   └── multilayer_job_manager.py # Job management service
│   └── api/
│       └── print.py                 # Enhanced (existing file)
frontend/
├── src/
│   ├── components/
│   │   ├── MultiLayerJobControl.vue
│   │   ├── JobProgressDisplay.vue
│   │   ├── ErrorDisplayPanel.vue
│   │   └── CriticalErrorModal.vue      # Persistent error modal with 'X' button
│   └── stores/
│       └── printJobStore.js         # Enhanced (existing file)
```

### Files to Modify
```
backend/
├── requirements.txt                 # Add OPC UA dependencies
├── app/dependencies.py             # Add OPC UA coordinator dependency
└── app/api/print.py                # Add multi-layer endpoints

frontend/
├── package.json                    # Add any new dependencies
└── src/views/PrintView.vue         # Integrate multi-layer components
```

## Backend Implementation

### 1. OPC UA Configuration (`backend/app/config/opcua_config.py`)

```python
"""
OPC UA Server Configuration
===========================

Configuration settings for OPC UA server hosted by FastAPI backend.
"""

import os
from typing import Dict, Optional
from dataclasses import dataclass

@dataclass
class OPCUAServerConfig:
    """OPC UA server configuration"""

    # Server settings
    endpoint: str = "opc.tcp://0.0.0.0:4843"
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
    server_name: str = "Recoater Backend OPC UA Server"
    application_uri: str = "urn:recoater:backend:opcua:server"

    # Aerosint API settings
    drum_upload_delay: float = 2.0  # seconds between drum uploads

    # Security settings
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: Optional[str] = None
    private_key_path: Optional[str] = None

    # Variable definitions to host
    variables: Dict[str, Dict] = None
    
    def __post_init__(self):
        """Initialize variable definitions"""
        if self.variables is None:
            self.variables = {
                # Job Control
                "job_active": {"type": "boolean", "initial": False},
                "total_layers": {"type": "int", "initial": 0},
                "current_layer": {"type": "int", "initial": 0},

                # Recoater Coordination
                "recoater_ready_to_print": {"type": "boolean", "initial": False},
                "recoater_layer_complete": {"type": "boolean", "initial": False},

                # System Status
                "backend_error": {"type": "boolean", "initial": False},
                "plc_error": {"type": "boolean", "initial": False}
            }
    
    @classmethod
    def from_environment(cls) -> 'OPCUAServerConfig':
        """Create configuration from environment variables"""
        return cls(
            endpoint=os.getenv("OPCUA_SERVER_ENDPOINT", "opc.tcp://0.0.0.0:4843"),
            namespace_uri=os.getenv("OPCUA_NAMESPACE_URI", "http://recoater.backend.server"),
            namespace_idx=int(os.getenv("OPCUA_NAMESPACE_IDX", "2")),
            server_name=os.getenv("OPCUA_SERVER_NAME", "Recoater Backend OPC UA Server"),
            certificate_path=os.getenv("OPCUA_CERTIFICATE_PATH"),
            private_key_path=os.getenv("OPCUA_PRIVATE_KEY_PATH")
        )

# Global configuration instance
opcua_server_config = OPCUAServerConfig.from_environment()
```

### 2. Multi-Layer Job Models (`backend/app/models/multilayer_job.py`)

```python
"""
Multi-Layer Job Models
======================

Data models for multi-layer print job state management.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum
import time
import uuid

class JobStatus(Enum):
    """Multi-layer job status enumeration"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    WAITING_FOR_PRINT_START = "waiting_for_print_start"
    RECOATER_ACTIVE = "recoater_active"
    WAITING_FOR_DEPOSITION = "waiting_for_deposition"
    LAYER_COMPLETE = "layer_complete"
    JOB_COMPLETE = "job_complete"
    ERROR = "error"
    CANCELLED = "cancelled"

@dataclass
class LayerData:
    """Individual layer information for multi-material printing"""
    layer_number: int
    z_height: float
    cli_data: bytes
    drum_id: int  # 0, 1, or 2 for the three drums
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class DrumState:
    """State management for individual drum"""
    drum_id: int
    file_id: Optional[str] = None
    is_ready: bool = False
    is_uploaded: bool = False
    status: str = "idle"  # idle, uploading, ready, printing, error
    error_message: str = ""
    last_layer_uploaded: int = 0

@dataclass
class MultiMaterialJobState:
    """Complete state management for multi-material print jobs with 3 drums"""
    
    # Job identification
    job_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    file_ids: Dict[int, str] = field(default_factory=dict)  # drum_id -> file_id mapping
    
    # Job scope
    total_layers: int = 0
    current_layer: int = 0
    remaining_layers: Dict[int, List[LayerData]] = field(default_factory=dict)  # drum_id -> layers
    
    # Job status
    is_active: bool = False
    status: JobStatus = JobStatus.IDLE
    
    # Timing information
    start_time: Optional[float] = None
    last_layer_time: Optional[float] = None
    estimated_completion: Optional[float] = None
    
    # Process state tracking
    waiting_for_print_start: bool = False
    waiting_for_layer_complete: bool = False
    
    # Multi-drum coordination
    drums: Dict[int, DrumState] = field(default_factory=lambda: {
        0: DrumState(drum_id=0),
        1: DrumState(drum_id=1), 
        2: DrumState(drum_id=2)
    })
    
    # Error handling
    error_message: str = ""
    retry_count: int = 0
    max_retries: int = 3
    
    # Configuration
    layer_timeout: float = 300.0  # 5 minutes per layer max
    drum_sync_timeout: float = 60.0  # 1 minute for all drums to sync
    
    def get_progress_percentage(self) -> float:
        """Calculate job progress percentage"""
        if self.total_layers == 0:
            return 0.0
        return ((self.current_layer - 1) / self.total_layers) * 100.0
    
    def get_estimated_time_remaining(self) -> Optional[float]:
        """Estimate remaining time based on current progress"""
        if not self.start_time or self.current_layer <= 1:
            return None
            
        elapsed_time = time.time() - self.start_time
        layers_completed = self.current_layer - 1
        
        if layers_completed == 0:
            return None
            
        time_per_layer = elapsed_time / layers_completed
        remaining_layers = self.total_layers - self.current_layer + 1
        
        return time_per_layer * remaining_layers
    
    def can_start_job(self) -> bool:
        """Check if multi-material job can be started"""
        return (
            not self.is_active and
            self.status in [JobStatus.IDLE, JobStatus.ERROR, JobStatus.CANCELLED] and
            self.total_layers > 0 and
            len(self.file_ids) == 3 and  # Must have all 3 drum files
            all(len(layers) > 0 for layers in self.remaining_layers.values()) and
            all(len(layers) == self.total_layers for layers in self.remaining_layers.values())  # All drums same layer count
        )
    
    def can_cancel_job(self) -> bool:
        """Check if job can be cancelled"""
        return (
            self.is_active and
            self.status not in [JobStatus.JOB_COMPLETE, JobStatus.CANCELLED, JobStatus.ERROR]
        )
    
    def all_drums_ready(self) -> bool:
        """Check if all drums are ready for next operation"""
        return all(drum.is_ready for drum in self.drums.values())
    
    def all_drums_uploaded(self) -> bool:
        """Check if all drums have uploaded current layer"""
        return all(drum.is_uploaded for drum in self.drums.values())
    
    def get_drum_errors(self) -> Dict[int, str]:
        """Get error messages from all drums"""
        return {drum_id: drum.error_message 
                for drum_id, drum in self.drums.items() 
                if drum.error_message}
    
    def reset_job(self):
        """Reset job state for new execution"""
        self.job_id = str(uuid.uuid4())
        self.current_layer = 0
        self.is_active = False
        self.status = JobStatus.IDLE
        self.start_time = None
        self.last_layer_time = None
        self.estimated_completion = None
        self.waiting_for_print_start = False
        self.waiting_for_layer_complete = False
        self.error_message = ""
        self.retry_count = 0
        
        # Reset all drum states
        for drum in self.drums.values():
            drum.is_ready = False
            drum.is_uploaded = False
            drum.status = "idle"
            drum.error_message = ""
            drum.last_layer_uploaded = 0

@dataclass 
class JobStatistics:
    """Job execution statistics for multi-material jobs"""
    total_time: float
    average_layer_time: float
    layers_completed: int
    errors_encountered: int
    retries_performed: int
    success_rate: float
    drum_statistics: Dict[int, Dict[str, Any]]  # Per-drum statistics

# Global job state instance
multimaterial_job_state = MultiMaterialJobState()
```

### 3. OPC UA Coordinator (`backend/app/services/opcua_coordinator.py`)

```python
"""
OPC UA Coordinator
==================

Manages OPC UA communication with the TwinCAT PLC for real-time coordination.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
from asyncua import Client, ua
from asyncua.common.subscription import DataChangeNotif

from app.config.opcua_config import opcua_config
from app.models.multilayer_job import multilayer_job_state, JobStatus

logger = logging.getLogger(__name__)

class OPCUACoordinator:
    """Manages OPC UA communication and coordination logic"""
    
    def __init__(self, config=None):
        self.config = config or opcua_config
        self.client: Optional[Client] = None
        self.nodes: Dict[str, Any] = {}
        self.subscription = None
        self._monitoring_task: Optional[asyncio.Task] = None
        self._connection_task: Optional[asyncio.Task] = None
        self._connected = False
        self._event_handlers: Dict[str, List[Callable]] = {}
        
    async def connect(self) -> bool:
        """Connect to PLC OPC UA server and set up subscriptions"""
        try:
            logger.info(f"Connecting to OPC UA server: {self.config.endpoint}")
            
            self.client = Client(self.config.endpoint)
            await self.client.connect()
            
            # Get node references
            self.nodes = {}
            for var_name, node_id in self.config.variable_nodes.items():
                try:
                    self.nodes[var_name] = await self.client.get_node(node_id)
                    logger.debug(f"Mapped OPC UA node: {var_name} -> {node_id}")
                except Exception as e:
                    logger.error(f"Failed to map node {var_name}: {e}")
                    
            if not self.nodes:
                raise Exception("No OPC UA nodes could be mapped")
            
            # Set up subscriptions for critical state changes
            await self._setup_subscriptions()
            
            self._connected = True
            logger.info("OPC UA connection established successfully")
            
            # Start connection monitoring
            self._connection_task = asyncio.create_task(self._monitor_connection())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to OPC UA server: {e}")
            self._connected = False
            return False
    
    async def _setup_subscriptions(self):
        """Set up OPC UA subscriptions for critical variables"""
        if not self.client:
            return
            
        try:
            self.subscription = await self.client.create_subscription(
                self.config.subscription_interval, 
                self
            )
            
            # Subscribe to critical state change variables
            critical_variables = [
                "galvo_scan_complete",
                "system_error",
                "recoater_layer_complete"
            ]
            
            nodes_to_subscribe = []
            for var_name in critical_variables:
                if var_name in self.nodes:
                    nodes_to_subscribe.append(self.nodes[var_name])
                    
            if nodes_to_subscribe:
                await self.subscription.subscribe_data_change(nodes_to_subscribe)
                logger.info(f"Subscribed to {len(nodes_to_subscribe)} OPC UA variables")
                
        except Exception as e:
            logger.error(f"Failed to set up OPC UA subscriptions: {e}")
    
    def datachange_notification(self, node, val, data: DataChangeNotif):
        """Handle OPC UA data change notifications"""
        try:
            # Find the variable name for this node
            node_name = None
            for name, node_ref in self.nodes.items():
                if node_ref == node:
                    node_name = name
                    break
                    
            if node_name:
                logger.debug(f"OPC UA data change: {node_name} = {val}")
                
                # Create async task to handle the event
                asyncio.create_task(self._handle_data_change(node_name, val))
                
        except Exception as e:
            logger.error(f"Error in datachange_notification: {e}")
    
    async def _handle_data_change(self, node_name: str, value: Any):
        """Handle OPC UA data change events"""
        try:
            # Handle specific events
            if node_name == "galvo_scan_complete" and value:
                await self._handle_galvo_complete()
            elif node_name == "system_error" and value:
                error_msg = await self.read_variable("error_message") or "Unknown error"
                await self._handle_system_error(error_msg)
            elif node_name == "recoater_layer_complete" and value:
                logger.info("Recoater layer completion detected via OPC UA")
                
            # Notify event handlers
            if node_name in self._event_handlers:
                for handler in self._event_handlers[node_name]:
                    try:
                        await handler(value)
                    except Exception as e:
                        logger.error(f"Error in event handler for {node_name}: {e}")
                        
        except Exception as e:
            logger.error(f"Error handling data change for {node_name}: {e}")
    
    # Note: _handle_galvo_complete method removed as part of simplified coordination
    # Galvo coordination is handled by the PLC, not the backend
                
        except Exception as e:
            logger.error(f"Error handling galvo completion: {e}")
            await self._handle_system_error(f"Galvo completion handling error: {e}")
    
    async def _handle_system_error(self, error_message: str):
        """Handle system errors - pause everything for operator review"""
        global multilayer_job_state
        
        logger.error(f"System error detected: {error_message}")
        
        multilayer_job_state.is_active = False
        multilayer_job_state.status = JobStatus.ERROR
        multilayer_job_state.error_message = error_message
        
        await self.write_variable("job_active", False)
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            
        logger.error(f"Multi-layer job paused for operator review: {error_message}")
    
    async def write_variable(self, var_name: str, value: Any) -> bool:
        """Write value to PLC variable"""
        if not self._connected or var_name not in self.nodes:
            logger.warning(f"Cannot write variable {var_name}: not connected or node not found")
            return False
            
        try:
            await self.nodes[var_name].write_value(value)
            logger.debug(f"OPC UA write: {var_name} = {value}")
            return True
        except Exception as e:
            logger.error(f"Failed to write OPC UA variable {var_name}: {e}")
            return False
    
    async def read_variable(self, var_name: str) -> Optional[Any]:
        """Read value from PLC variable"""
        if not self._connected or var_name not in self.nodes:
            logger.warning(f"Cannot read variable {var_name}: not connected or node not found")
            return None
            
        try:
            value = await self.nodes[var_name].read_value()
            logger.debug(f"OPC UA read: {var_name} = {value}")
            return value
        except Exception as e:
            logger.error(f"Failed to read OPC UA variable {var_name}: {e}")
            return None
    
    async def write_multiple_variables(self, variables: Dict[str, Any]) -> bool:
        """Write multiple variables in a single operation"""
        if not self._connected:
            return False
            
        try:
            nodes_to_write = []
            values_to_write = []
            
            for var_name, value in variables.items():
                if var_name in self.nodes:
                    nodes_to_write.append(self.nodes[var_name])
                    values_to_write.append(value)
                    
            if nodes_to_write:
                await self.client.write_values(nodes_to_write, values_to_write)
                logger.debug(f"OPC UA batch write: {variables}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to write multiple OPC UA variables: {e}")
            return False
    
    async def reset_coordination_flags(self):
        """Reset all coordination flags to false"""
        reset_variables = {
            "recoater_ready_to_print": False,
            "recoater_start_signal": False,
            "recoater_layer_complete": False,
            "galvo_ready_to_scan": False,
            "galvo_scan_complete": False
        }
        
        await self.write_multiple_variables(reset_variables)
        logger.info("Reset all coordination flags")
    
    def add_event_handler(self, variable_name: str, handler: Callable):
        """Add event handler for variable changes"""
        if variable_name not in self._event_handlers:
            self._event_handlers[variable_name] = []
        self._event_handlers[variable_name].append(handler)
    
    def remove_event_handler(self, variable_name: str, handler: Callable):
        """Remove event handler for variable changes"""
        if variable_name in self._event_handlers:
            try:
                self._event_handlers[variable_name].remove(handler)
            except ValueError:
                pass
    
    async def _monitor_connection(self):
        """Monitor OPC UA connection and reconnect if needed"""
        while self._connected:
            try:
                # Simple connection test
                await self.read_variable("job_active")
                await asyncio.sleep(10)  # Check every 10 seconds
            except Exception as e:
                logger.warning(f"OPC UA connection lost: {e}")
                self._connected = False
                await self._attempt_reconnection()
    
    async def _attempt_reconnection(self):
        """Attempt to reconnect to OPC UA server"""
        max_retries = 5
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Attempting OPC UA reconnection ({attempt + 1}/{max_retries})")
                await self.disconnect()
                await asyncio.sleep(retry_delay)
                
                if await self.connect():
                    logger.info("OPC UA reconnection successful")
                    return
                    
            except Exception as e:
                logger.error(f"Reconnection attempt {attempt + 1} failed: {e}")
                
            retry_delay = min(retry_delay * 2, 60)  # Exponential backoff, max 60s
        
        logger.error("Failed to reconnect to OPC UA server after all retries")
    
    async def disconnect(self):
        """Disconnect from OPC UA server"""
        try:
            self._connected = False
            
            if self._connection_task:
                self._connection_task.cancel()
                self._connection_task = None
                
            if self._monitoring_task:
                self._monitoring_task.cancel()
                self._monitoring_task = None
                
            if self.subscription:
                await self.subscription.delete()
                self.subscription = None
                
            if self.client:
                await self.client.disconnect()
                self.client = None
                
            self.nodes.clear()
            logger.info("Disconnected from OPC UA server")
            
        except Exception as e:
            logger.error(f"Error during OPC UA disconnection: {e}")
    
    def is_connected(self) -> bool:
        """Check if OPC UA client is connected"""
        return self._connected and self.client is not None

# Global coordinator instance
opcua_coordinator = OPCUACoordinator()
```

### 4. Enhanced Print API (`backend/app/api/print.py` - Additions)

Add these imports and components to the existing `print.py` file:

```python
# Add these imports to the existing imports section
import asyncio
import time
import uuid
import datetime
import logging
from dataclasses import asdict

from app.models.multilayer_job import (
    MultiLayerJobState, 
    multilayer_job_state, 
    JobStatus, 
    LayerData
)
from app.services.opcua_coordinator import opcua_coordinator

# Add missing imports that were incomplete in the original file
import uuid
import datetime

# Add these new Pydantic models after existing models
class MultiMaterialJobRequest(BaseModel):
    """Request model for starting multi-material jobs."""
    file_ids: Dict[int, str] = Field(..., description="Mapping of drum_id (0-2) to file_id")
    
    @validator('file_ids')
    def validate_drum_ids(cls, v):
        if not all(drum_id in [0, 1, 2] for drum_id in v.keys()):
            raise ValueError("All drum IDs must be 0, 1, or 2")
        if len(v) != 3:
            raise ValueError("Must provide exactly 3 files (one for each drum)")
        return v

class MultiMaterialJobResponse(BaseModel):
    """Response model for multi-material job operations."""
    success: bool
    message: str
    job_id: str
    file_ids: Dict[int, str]
    total_layers: int
    current_layer: int
    drums_status: Dict[int, str]

class MultiMaterialJobStatusResponse(BaseModel):
    """Response model for multi-material job status."""
    is_active: bool
    job_id: Optional[str] = None
    file_ids: Dict[int, str] = Field(default_factory=dict)
    current_layer: int
    total_layers: int
    progress_percentage: float
    status: str
    waiting_for_print_start: bool
    waiting_for_layer_complete: bool
    system_error: bool
    error_message: str
    estimated_time_remaining: Optional[float] = None
    start_time: Optional[float] = None
    drums: Dict[int, Dict[str, Any]] = Field(default_factory=dict)

class DrumStatusResponse(BaseModel):
    """Response model for individual drum status."""
    drum_id: int
    file_id: Optional[str]
    is_ready: bool
    is_uploaded: bool
    status: str
    error_message: str
    last_layer_uploaded: int

# Legacy single-drum models (kept for backward compatibility)
class MultiLayerJobResponse(BaseModel):
    """Response model for multi-layer job operations."""
    success: bool
    message: str
    job_id: str
    file_id: str
    total_layers: int
    current_layer: int

class MultiLayerJobStatusResponse(BaseModel):
    """Response model for multi-layer job status."""
    is_active: bool
    job_id: Optional[str] = None
    file_id: Optional[str] = None
    current_layer: int
    total_layers: int
    progress_percentage: float
    status: str
    waiting_for_print_start: bool
    waiting_for_layer_complete: bool
    system_error: bool
    error_message: str
    estimated_time_remaining: Optional[float] = None
    start_time: Optional[float] = None

# Fix the incomplete functions in the original file
def create_temp_cli_file(cli_data: bytes, file_prefix: str = "layer_range") -> PathLib:
    """
    Create a temporary CLI file for verification purposes.

    Args:
        cli_data: The ASCII CLI file data as bytes
        file_prefix: Prefix for the temporary file name

    Returns:
        Path to the created temporary file
    """
    # Ensure temp directory exists
    TEMP_CLI_DIR.mkdir(exist_ok=True)

    # Create temporary file with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    temp_filename = f"{file_prefix}_{timestamp}_{uuid.uuid4().hex[:8]}.cli"
    temp_file_path = TEMP_CLI_DIR / temp_filename

    # Write CLI data to file
    with open(temp_file_path, 'wb') as f:
        f.write(cli_data)

    logger.info(f"Created temporary CLI file: {temp_file_path}")
    return temp_file_path


def cleanup_temp_cli_file(file_path: PathLib) -> bool:
    """
    Delete a temporary CLI file.

    Args:
        file_path: Path to the temporary file to delete

    Returns:
        True if file was deleted successfully, False otherwise
    """
    try:
        if file_path.exists():
            file_path.unlink()
            logger.info(f"Deleted temporary CLI file: {file_path}")
            return True
        return False
    except Exception as e:
        logger.error(f"Error deleting temporary CLI file {file_path}: {e}")
        return False

# Add these new endpoints at the end of the file

@router.post("/cli/start-multimaterial-job", response_model=MultiMaterialJobResponse)
async def start_multimaterial_print_job(
    job_request: MultiMaterialJobRequest,
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> MultiMaterialJobResponse:
    """
    Start a coordinated multi-material print job with 3 drums.
    
    This endpoint initiates a fully automated multi-material print process that
    coordinates between all 3 recoater drums, PLC, and galvo systems.
    """
    global multimaterial_job_state
    
    try:
        logger.info(f"Starting multi-material job with files: {job_request.file_ids}")
        
        # Validate prerequisites
        if len(job_request.file_ids) != 3:
            raise HTTPException(status_code=400, detail="Must provide exactly 3 CLI files (one for each drum)")
            
        # Validate all files exist
        for drum_id, file_id in job_request.file_ids.items():
            if file_id not in cli_file_cache:
                raise HTTPException(status_code=404, detail=f"CLI file {file_id} for drum {drum_id} not found")
        
        if multimaterial_job_state.is_active:
            raise HTTPException(
                status_code=409, 
                detail=f"Another job is already active (Job ID: {multimaterial_job_state.job_id})"
            )
        
        # Validate all CLI files have same layer count
        layer_counts = {}
        parsed_files = {}
        for drum_id, file_id in job_request.file_ids.items():
            parsed_data = cli_file_cache[file_id]
            if not parsed_data.layers:
                raise HTTPException(status_code=400, detail=f"CLI file for drum {drum_id} contains no layers")
            layer_counts[drum_id] = len(parsed_data.layers)
            parsed_files[drum_id] = parsed_data
        
        # Check all drums have same layer count
        if len(set(layer_counts.values())) > 1:
            raise HTTPException(
                status_code=400, 
                detail=f"All CLI files must have the same number of layers. Found: {layer_counts}"
            )
            
        total_layers = list(layer_counts.values())[0]
        
        # Initialize job state
        multimaterial_job_state.reset_job()
        multimaterial_job_state.file_ids = job_request.file_ids
        multimaterial_job_state.total_layers = total_layers
        multimaterial_job_state.current_layer = 1
        multimaterial_job_state.is_active = True
        multimaterial_job_state.status = JobStatus.INITIALIZING
        multimaterial_job_state.start_time = time.time()
        
        # Convert layers to LayerData objects for each drum
        for drum_id, parsed_data in parsed_files.items():
            multimaterial_job_state.remaining_layers[drum_id] = [
                LayerData(
                    layer_number=i + 1,
                    z_height=layer.get('z_height', 0.0),
                    cli_data=b'',  # Will be generated on demand
                    drum_id=drum_id,
                    metadata=layer
                )
                for i, layer in enumerate(parsed_data.layers)
            ]
            
            # Update drum state
            multimaterial_job_state.drums[drum_id].file_id = job_request.file_ids[drum_id]
            multimaterial_job_state.drums[drum_id].status = "initializing"
        
        # Connect to OPC UA if needed
        if not opcua_coordinator.is_connected():
            connected = await opcua_coordinator.connect()
            if not connected:
                raise HTTPException(
                    status_code=503,
                    detail="Unable to connect to PLC. Check OPC UA server status."
                )
        
        # Initialize PLC variables for multi-material operation
        await opcua_coordinator.write_multiple_variables({
            "job_active": True,
            "total_layers": multimaterial_job_state.total_layers,
            "current_layer": 1,
            "system_error": False,
            "error_message": "",
            "multimaterial_mode": True,
            "drums_count": 3
        })
        
        # Reset all coordination flags
        await opcua_coordinator.reset_coordination_flags()
        
        # Import here to avoid circular imports
        from app.services.coordination_engine import coordination_engine
        
        # Start the multi-material coordination process
        success = await coordination_engine.start_multimaterial_job(multimaterial_job_state)
        
        if not success:
            multilayer_job_state.is_active = False
            multilayer_job_state.status = JobStatus.ERROR
            raise HTTPException(
                status_code=500,
                detail="Failed to start coordination process"
            )
        
        return MultiLayerJobResponse(
            success=True,
            message=f"Multi-layer job started successfully",
            job_id=multilayer_job_state.job_id,
            file_id=file_id,
            total_layers=multilayer_job_state.total_layers,
            current_layer=multilayer_job_state.current_layer
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error starting multi-layer job: {e}")
        multilayer_job_state.is_active = False
        multilayer_job_state.status = JobStatus.ERROR
        multilayer_job_state.error_message = str(e)
        raise HTTPException(status_code=500, detail=f"Failed to start job: {e}")

@router.get("/multimaterial-job/status", response_model=MultiMaterialJobStatusResponse)
async def get_multimaterial_job_status() -> MultiMaterialJobStatusResponse:
    """Get the current status of the multi-material print job"""
    global multimaterial_job_state
    
    # Read error status from PLC if connected
    system_error = False
    error_message = ""
    if opcua_coordinator.is_connected():
        try:
            system_error = await opcua_coordinator.read_variable("system_error") or False
            error_message = await opcua_coordinator.read_variable("error_message") or ""
        except Exception as e:
            logger.warning(f"Failed to read error status from PLC: {e}")
    
    # Use local error state if PLC error state is not available
    if not system_error and multimaterial_job_state.status == JobStatus.ERROR:
        system_error = True
        error_message = multimaterial_job_state.error_message
    
    # Prepare drum status data
    drums_data = {}
    for drum_id, drum in multimaterial_job_state.drums.items():
        drums_data[drum_id] = {
            "file_id": drum.file_id,
            "is_ready": drum.is_ready,
            "is_uploaded": drum.is_uploaded,
            "status": drum.status,
            "error_message": drum.error_message,
            "last_layer_uploaded": drum.last_layer_uploaded
        }
    
    return MultiMaterialJobStatusResponse(
        is_active=multimaterial_job_state.is_active,
        job_id=multimaterial_job_state.job_id,
        file_ids=multimaterial_job_state.file_ids,
        current_layer=multimaterial_job_state.current_layer,
        total_layers=multimaterial_job_state.total_layers,
        progress_percentage=multimaterial_job_state.get_progress_percentage(),
        status=multimaterial_job_state.status.value,
        waiting_for_print_start=multimaterial_job_state.waiting_for_print_start,
        waiting_for_layer_complete=multimaterial_job_state.waiting_for_layer_complete,
        system_error=system_error,
        error_message=error_message,
        estimated_time_remaining=multimaterial_job_state.get_estimated_time_remaining(),
        start_time=multimaterial_job_state.start_time,
        drums=drums_data
    )

@router.get("/multimaterial-job/drum-status/{drum_id}", response_model=DrumStatusResponse)
async def get_drum_status(drum_id: int) -> DrumStatusResponse:
    """Get the status of a specific drum"""
    global multimaterial_job_state
    
    if drum_id not in [0, 1, 2]:
        raise HTTPException(status_code=400, detail="Invalid drum ID. Must be 0, 1, or 2")
    
    if drum_id not in multimaterial_job_state.drums:
        raise HTTPException(status_code=404, detail=f"Drum {drum_id} not found")
    
    drum = multimaterial_job_state.drums[drum_id]
    
    return DrumStatusResponse(
        drum_id=drum.drum_id,
        file_id=drum.file_id,
        is_ready=drum.is_ready,
        is_uploaded=drum.is_uploaded,
        status=drum.status,
        error_message=drum.error_message,
        last_layer_uploaded=drum.last_layer_uploaded
    )

# Legacy endpoint (kept for backward compatibility)
@router.get("/multilayer-job/status", response_model=MultiLayerJobStatusResponse)
async def get_multilayer_job_status() -> MultiLayerJobStatusResponse:
    """Get the current status of the multi-layer print job (legacy single-drum)"""
    # This endpoint is kept for backward compatibility
    # In a multi-material system, this would return data for drum 0 or aggregate data
    global multimaterial_job_state
    
    # Read error status from PLC if connected
    system_error = False
    error_message = ""
    if opcua_coordinator.is_connected():
        try:
            system_error = await opcua_coordinator.read_variable("system_error") or False
            error_message = await opcua_coordinator.read_variable("error_message") or ""
        except Exception as e:
            logger.warning(f"Failed to read error status from PLC: {e}")
    
    # Use local error state if PLC error state is not available
    if not system_error and multimaterial_job_state.status == JobStatus.ERROR:
        system_error = True
        error_message = multimaterial_job_state.error_message
    
    # Return aggregate data or drum 0 data for backward compatibility
    file_id = multimaterial_job_state.file_ids.get(0, "")
    
    return MultiLayerJobStatusResponse(
        is_active=multimaterial_job_state.is_active,
        job_id=multimaterial_job_state.job_id,
        file_id=file_id,
        current_layer=multimaterial_job_state.current_layer,
        total_layers=multimaterial_job_state.total_layers,
        progress_percentage=multimaterial_job_state.get_progress_percentage(),
        status=multimaterial_job_state.status.value,
        waiting_for_print_start=multimaterial_job_state.waiting_for_print_start,
        waiting_for_layer_complete=multimaterial_job_state.waiting_for_layer_complete,
        system_error=system_error,
        error_message=error_message,
        estimated_time_remaining=multimaterial_job_state.get_estimated_time_remaining(),
        start_time=multimaterial_job_state.start_time
    )

@router.post("/multilayer-job/cancel")
async def cancel_multilayer_job() -> Dict[str, Any]:
    """Cancel the current multi-layer print job"""
    global multilayer_job_state
    
    if not multilayer_job_state.is_active:
        raise HTTPException(status_code=400, detail="No active job to cancel")
        
    try:
        logger.info(f"Cancelling multi-layer job {multilayer_job_state.job_id}")
        
        # Cancel current Aerosint job if possible
        try:
            recoater_client = get_recoater_client()
            recoater_client.cancel_print_job()
            logger.info("Cancelled current Aerosint print job")
        except Exception as e:
            logger.warning(f"Failed to cancel Aerosint job: {e}")
        
        # Signal cancellation through OPC UA
        if opcua_coordinator.is_connected():
            await opcua_coordinator.write_multiple_variables({
                "system_error": True,
                "error_message": "Job cancelled by user",
                "job_active": False
            })
        
        # Update job state
        multilayer_job_state.is_active = False
        multilayer_job_state.status = JobStatus.CANCELLED
        multilayer_job_state.error_message = "Job cancelled by user"
        
        logger.info(f"Multi-layer job {multilayer_job_state.job_id} cancelled successfully")
        
        return {
            "success": True,
            "message": "Multi-layer job cancelled successfully",
            "job_id": multilayer_job_state.job_id
        }
        
    except Exception as e:
        logger.error(f"Error cancelling job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel job: {e}")

@router.post("/multilayer-job/clear-error")
async def clear_system_error() -> Dict[str, Any]:
    """Clear system error state to allow new jobs"""
    try:
        logger.info("Clearing system error state")
        
        # Clear error state in PLC
        if opcua_coordinator.is_connected():
            await opcua_coordinator.write_multiple_variables({
                "system_error": False,
                "error_message": ""
            })
        
        # Clear local error state
        if multilayer_job_state.status == JobStatus.ERROR:
            multilayer_job_state.status = JobStatus.IDLE
            multilayer_job_state.error_message = ""
            multilayer_job_state.retry_count = 0
        
        return {
            "success": True,
            "message": "System error cleared successfully"
        }
        
    except Exception as e:
        logger.error(f"Error clearing system error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear error: {e}")

@router.get("/multilayer-job/statistics")
async def get_job_statistics() -> Dict[str, Any]:
    """Get statistics for the current or last multi-layer job"""
    global multilayer_job_state
    
    if not multilayer_job_state.start_time:
        raise HTTPException(status_code=404, detail="No job statistics available")
    
    current_time = time.time()
    elapsed_time = current_time - multilayer_job_state.start_time
    completed_layers = max(0, multilayer_job_state.current_layer - 1)
    
    stats = {
        "job_id": multilayer_job_state.job_id,
        "total_time": elapsed_time,
        "layers_completed": completed_layers,
        "layers_total": multilayer_job_state.total_layers,
        "average_layer_time": elapsed_time / completed_layers if completed_layers > 0 else 0,
        "estimated_completion": multilayer_job_state.get_estimated_time_remaining(),
        "progress_percentage": multilayer_job_state.get_progress_percentage(),
        "status": multilayer_job_state.status.value,
        "retry_count": multilayer_job_state.retry_count
    }
    
    return stats
```

### 5. Enhanced Dependencies (`backend/app/dependencies.py` - Addition)

Add this function to the existing dependencies file:

```python
# Add this import
from app.services.opcua_coordinator import opcua_coordinator

# Add this dependency function
async def get_opcua_coordinator():
    """Dependency to get the OPC UA coordinator instance"""
    if not opcua_coordinator.is_connected():
        # Attempt to connect if not already connected
        await opcua_coordinator.connect()
    return opcua_coordinator
```

### 6. Drum Upload Implementation with Delay

**Critical Implementation Note**: When uploading layers to multiple drums, add a 2-second delay between uploads to prevent overloading the Aerosint server.

```python
# Example implementation in coordination engine or job manager
async def upload_layer_to_all_drums(self, layer_num: int) -> bool:
    """Upload specified layer to all 3 drums with delay between uploads"""
    try:
        for drum_id in [0, 1, 2]:
            layer_data = self.get_layer_for_drum(drum_id, layer_num)
            success = await self.recoater_client.upload_layer(drum_id, layer_data)
            if not success:
                logger.error(f"Failed to upload layer {layer_num} to drum {drum_id}")
                return False

            # Add delay between drum uploads to prevent overloading Aerosint server
            if drum_id < 2:  # Don't delay after the last drum
                await asyncio.sleep(opcua_server_config.drum_upload_delay)

        return True
    except Exception as e:
        logger.error(f"Error uploading layer {layer_num}: {e}")
        return False
```

### 7. Requirements Update (`backend/requirements.txt` - Addition)

Add these lines to the existing requirements.txt:

```txt
asyncua>=1.0.0
asyncio-mqtt>=0.11.0
```

## Frontend Implementation

### Critical Error Modal Component (`frontend/src/components/CriticalErrorModal.vue`)

```vue
<template>
  <div v-if="isVisible" class="critical-error-overlay">
    <div class="critical-error-modal">
      <div class="error-header">
        <h2 class="error-title">⚠️ Critical System Error</h2>
        <button
          @click="acknowledgeError"
          class="close-button"
          data-testid="error-modal-close"
        >
          ✕
        </button>
      </div>

      <div class="error-content">
        <div class="error-message">
          {{ errorData.message }}
        </div>

        <div v-if="errorData.details" class="error-details">
          <strong>Details:</strong> {{ errorData.details }}
        </div>

        <div class="error-metadata">
          <div><strong>Job ID:</strong> {{ errorData.job_id }}</div>
          <div><strong>Time:</strong> {{ formatTimestamp(errorData.timestamp) }}</div>
        </div>
      </div>

      <div class="error-actions">
        <button
          @click="acknowledgeError"
          class="acknowledge-button"
          data-testid="acknowledge-error-button"
        >
          Acknowledge Error
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useToast } from 'vue-toastification'

export default {
  name: 'CriticalErrorModal',
  props: {
    errorData: {
      type: Object,
      default: () => ({})
    },
    isVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['acknowledge'],
  setup(props, { emit }) {
    const toast = useToast()

    const acknowledgeError = () => {
      // Log acknowledgment
      console.log('Critical error acknowledged by operator:', props.errorData)

      // Emit acknowledgment event
      emit('acknowledge', props.errorData)

      // Show confirmation
      toast.info('Error acknowledged. Operations can be resumed.')
    }

    const formatTimestamp = (timestamp) => {
      return new Date(timestamp * 1000).toLocaleString()
    }

    return {
      acknowledgeError,
      formatTimestamp
    }
  }
}
</script>

<style scoped>
.critical-error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.critical-error-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 3px solid #dc2626;
}

.error-header {
  background: #dc2626;
  color: white;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.error-content {
  padding: 1.5rem;
}

.error-message {
  font-size: 1.1rem;
  font-weight: bold;
  color: #dc2626;
  margin-bottom: 1rem;
}

.error-details {
  background: #f3f4f6;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-family: monospace;
  font-size: 0.9rem;
}

.error-metadata {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6b7280;
}

.error-actions {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

.acknowledge-button {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  font-size: 1rem;
}

.acknowledge-button:hover {
  background: #b91c1c;
}
</style>
```

## Frontend Implementation

### 1. Multi-Layer Job Control Component (`frontend/src/components/MultiLayerJobControl.vue`)

```vue
<template>
  <div class="multilayer-job-control">
    <div class="job-control-header">
      <h3>Multi-Layer Print Job</h3>
      <div class="job-status-badge" :class="statusClass">
        {{ formatStatus(jobStatus.status) }}
      </div>
    </div>
    
    <!-- Job Start Section -->
    <div v-if="!jobStatus.is_active" class="job-start-section">
      <div class="cli-file-info" v-if="selectedFile">
        <p><strong>Selected File:</strong> {{ selectedFile.filename }}</p>
        <p><strong>Total Layers:</strong> {{ selectedFile.total_layers }}</p>
      </div>
      
      <div class="drum-selection">
        <label for="drum-select">Target Drum:</label>
        <select id="drum-select" v-model="selectedDrum" :disabled="!canStartJob">
          <option value="0">Drum 0</option>
          <option value="1">Drum 1</option>
          <option value="2">Drum 2</option>
        </select>
      </div>
      
      <button 
        class="start-job-btn"
        :disabled="!canStartJob"
        @click="startMultiLayerJob"
      >
        <i class="icon-play"></i>
        Start Multi-Layer Job
      </button>
      
      <div v-if="!canStartJob" class="start-disabled-reason">
        {{ startDisabledReason }}
      </div>
    </div>
    
    <!-- Active Job Section -->
    <div v-else class="active-job-section">
      <JobProgressDisplay :job-status="jobStatus" />
      
      <div class="job-controls">
        <button 
          class="cancel-job-btn"
          @click="cancelJob"
          :disabled="!jobStatus.is_active"
        >
          <i class="icon-stop"></i>
          Cancel Job
        </button>
      </div>
    </div>
    
    <!-- Error Section -->
    <div v-if="jobStatus.system_error" class="error-section">
      <ErrorDisplayPanel 
        :error-message="jobStatus.error_message"
        @clear-error="clearError"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useToast } from 'vue-toastification'
import JobProgressDisplay from './JobProgressDisplay.vue'
import ErrorDisplayPanel from './ErrorDisplayPanel.vue'
import { usePrintJobStore } from '@/stores/printJobStore'

export default {
  name: 'MultiLayerJobControl',
  components: {
    JobProgressDisplay,
    ErrorDisplayPanel
  },
  props: {
    selectedFile: {
      type: Object,
      default: null
    }
  },
  emits: ['job-started', 'job-completed', 'job-cancelled'],
  setup(props, { emit }) {
    const toast = useToast()
    const printJobStore = usePrintJobStore()
    
    const selectedDrum = ref(0)
    const isLoading = ref(false)
    const statusPollingInterval = ref(null)
    
    const jobStatus = computed(() => printJobStore.multiLayerJob)
    
    const canStartJob = computed(() => {
      return props.selectedFile && 
             props.selectedFile.total_layers > 0 && 
             !jobStatus.value.is_active &&
             !jobStatus.value.system_error &&
             !isLoading.value
    })
    
    const startDisabledReason = computed(() => {
      if (!props.selectedFile) return 'No CLI file selected'
      if (props.selectedFile.total_layers === 0) return 'CLI file contains no layers'
      if (jobStatus.value.is_active) return 'Another job is already active'
      if (jobStatus.value.system_error) return 'System error - clear error first'
      if (isLoading.value) return 'Processing...'
      return ''
    })
    
    const statusClass = computed(() => {
      const status = jobStatus.value.status
      if (status === 'error' || jobStatus.value.system_error) return 'status-error'
      if (status === 'job_complete') return 'status-complete'
      if (jobStatus.value.is_active) return 'status-active'
      return 'status-idle'
    })
    
    const formatStatus = (status) => {
      return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
    
    const startMultiLayerJob = async () => {
      if (!canStartJob.value) return
      
      try {
        isLoading.value = true
        
        await printJobStore.startMultiLayerJob(
          props.selectedFile.file_id,
          selectedDrum.value
        )
        
        toast.success('Multi-layer job started successfully')
        emit('job-started', {
          fileId: props.selectedFile.file_id,
          drumId: selectedDrum.value
        })
        
        startStatusPolling()
        
      } catch (error) {
        toast.error(`Failed to start job: ${error.message}`)
      } finally {
        isLoading.value = false
      }
    }
    
    const cancelJob = async () => {
      try {
        await printJobStore.cancelMultiLayerJob()
        toast.success('Job cancelled successfully')
        emit('job-cancelled')
        stopStatusPolling()
      } catch (error) {
        toast.error(`Failed to cancel job: ${error.message}`)
      }
    }
    
    const clearError = async () => {
      try {
        await printJobStore.clearSystemError()
        toast.success('Error cleared successfully')
      } catch (error) {
        toast.error(`Failed to clear error: ${error.message}`)
      }
    }
    
    const startStatusPolling = () => {
      if (statusPollingInterval.value) return
      
      statusPollingInterval.value = setInterval(async () => {
        try {
          await printJobStore.updateJobStatus()
          
          // Check for job completion
          if (jobStatus.value.status === 'job_complete' && jobStatus.value.is_active === false) {
            emit('job-completed')
            stopStatusPolling()
            toast.success('Multi-layer job completed successfully!')
          }
        } catch (error) {
          console.error('Error updating job status:', error)
        }
      }, 2000) // Poll every 2 seconds
    }
    
    const stopStatusPolling = () => {
      if (statusPollingInterval.value) {
        clearInterval(statusPollingInterval.value)
        statusPollingInterval.value = null
      }
    }
    
    onMounted(() => {
      // Initial status update
      printJobStore.updateJobStatus()
      
      // Start polling if job is active
      if (jobStatus.value.is_active) {
        startStatusPolling()
      }
    })
    
    onUnmounted(() => {
      stopStatusPolling()
    })
    
    return {
      selectedDrum,
      isLoading,
      jobStatus,
      canStartJob,
      startDisabledReason,
      statusClass,
      formatStatus,
      startMultiLayerJob,
      cancelJob,
      clearError
    }
  }
}
</script>

<style scoped>
.multilayer-job-control {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.job-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.job-status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-idle { background: #f0f0f0; color: #666; }
.status-active { background: #e3f2fd; color: #1976d2; }
.status-complete { background: #e8f5e8; color: #2e7d32; }
.status-error { background: #ffebee; color: #c62828; }

.job-start-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.cli-file-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 6px;
}

.cli-file-info p {
  margin: 5px 0;
}

.drum-selection {
  display: flex;
  align-items: center;
  gap: 10px;
}

.drum-selection select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.start-job-btn {
  background: #4caf50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.3s;
}

.start-job-btn:hover:not(:disabled) {
  background: #45a049;
}

.start-job-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.start-disabled-reason {
  color: #666;
  font-size: 14px;
  font-style: italic;
}

.active-job-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.job-controls {
  display: flex;
  justify-content: center;
}

.cancel-job-btn {
  background: #f44336;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.3s;
}

.cancel-job-btn:hover:not(:disabled) {
  background: #d32f2f;
}

.cancel-job-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-section {
  margin-top: 20px;
}

.icon-play::before { content: '▶'; }
.icon-stop::before { content: '⏹'; }
</style>
```

### 2. Job Progress Display Component (`frontend/src/components/JobProgressDisplay.vue`)

```vue
<template>
  <div class="job-progress-display">
    <div class="progress-header">
      <h4>Job Progress</h4>
      <div class="job-id">Job ID: {{ jobStatus.job_id }}</div>
    </div>
    
    <div class="progress-stats">
      <div class="stat-item">
        <label>Current Layer:</label>
        <span class="stat-value">{{ jobStatus.current_layer }} / {{ jobStatus.total_layers }}</span>
      </div>
      
      <div class="stat-item">
        <label>Progress:</label>
        <span class="stat-value">{{ Math.round(jobStatus.progress_percentage) }}%</span>
      </div>
      
      <div class="stat-item" v-if="jobStatus.estimated_time_remaining">
        <label>Est. Remaining:</label>
        <span class="stat-value">{{ formatTime(jobStatus.estimated_time_remaining) }}</span>
      </div>
      
      <div class="stat-item" v-if="jobStatus.start_time">
        <label>Elapsed Time:</label>
        <span class="stat-value">{{ formatElapsedTime() }}</span>
      </div>
    </div>
    
    <div class="progress-bar-container">
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: `${jobStatus.progress_percentage}%` }"
        ></div>
      </div>
      <div class="progress-text">
        Layer {{ jobStatus.current_layer }} of {{ jobStatus.total_layers }}
      </div>
    </div>
    
    <div class="current-status">
      <div class="status-item" :class="{ active: jobStatus.waiting_for_print_start }">
        <div class="status-icon">⏳</div>
        <div class="status-text">Waiting for Print Start</div>
      </div>
      
      <div class="status-item" :class="{ active: jobStatus.waiting_for_layer_complete }">
        <div class="status-icon">🔄</div>
        <div class="status-text">Layer Deposition</div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'JobProgressDisplay',
  props: {
    jobStatus: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const formatTime = (seconds) => {
      if (!seconds || seconds < 0) return '--'
      
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`
      } else if (minutes > 0) {
        return `${minutes}m ${secs}s`
      } else {
        return `${secs}s`
      }
    }
    
    const formatElapsedTime = () => {
      if (!props.jobStatus.start_time) return '--'
      
      const elapsed = Date.now() / 1000 - props.jobStatus.start_time
      return formatTime(elapsed)
    }
    
    return {
      formatTime,
      formatElapsedTime
    }
  }
}
</script>

<style scoped>
.job-progress-display {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.job-id {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.progress-bar-container {
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.current-status {
  display: flex;
  justify-content: space-around;
  gap: 10px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  background: white;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  flex: 1;
}

.status-item.active {
  border-color: #007bff;
  background: #e3f2fd;
}

.status-icon {
  font-size: 24px;
}

.status-text {
  font-size: 12px;
  text-align: center;
  font-weight: 500;
  color: #666;
}

.status-item.active .status-text {
  color: #007bff;
}

@media (max-width: 768px) {
  .progress-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .current-status {
    flex-direction: column;
  }
}
</style>
```

### 3. Enhanced Print Job Store (`frontend/src/stores/printJobStore.js` - Enhancement)

Add these methods to the existing store:

```javascript
// Add to existing store actions
export const usePrintJobStore = defineStore('printJob', {
  state: () => ({
    // ... existing state
    multiLayerJob: {
      isActive: false,
      jobId: null,
      fileId: null,
      currentLayer: 0,
      totalLayers: 0,
      progressPercentage: 0,
      status: 'idle',
      waitingForPrintStart: false,
      waitingForLayerComplete: false,
      waitingForGalvo: false,
      systemError: false,
      errorMessage: '',
      estimatedTimeRemaining: null,
      startTime: null
    }
  }),

  actions: {
    // ... existing actions

    async startMultiLayerJob(fileId, drumId = 0) {
      try {
        const response = await this.$api.post(`/print/cli/${fileId}/start-multilayer-job`, {
          drum_id: drumId
        })

        if (response.data.success) {
          await this.updateJobStatus()
          return response.data
        } else {
          throw new Error(response.data.message || 'Failed to start multi-layer job')
        }
      } catch (error) {
        console.error('Error starting multi-layer job:', error)
        throw error
      }
    },

    async updateJobStatus() {
      try {
        const response = await this.$api.get('/print/multilayer-job/status')
        this.multiLayerJob = response.data
        return response.data
      } catch (error) {
        console.error('Error updating job status:', error)
        throw error
      }
    },

    async cancelMultiLayerJob() {
      try {
        const response = await this.$api.post('/print/multilayer-job/cancel')
        
        if (response.data.success) {
          await this.updateJobStatus()
          return response.data
        } else {
          throw new Error(response.data.message || 'Failed to cancel job')
        }
      } catch (error) {
        console.error('Error cancelling job:', error)
        throw error
      }
    },

    async clearSystemError() {
      try {
        const response = await this.$api.post('/print/multilayer-job/clear-error')
        
        if (response.data.success) {
          await this.updateJobStatus()
          return response.data
        } else {
          throw new Error(response.data.message || 'Failed to clear error')
        }
      } catch (error) {
        console.error('Error clearing system error:', error)
        throw error
      }
    },

    async getJobStatistics() {
      try {
        const response = await this.$api.get('/print/multilayer-job/statistics')
        return response.data
      } catch (error) {
        console.error('Error getting job statistics:', error)
        throw error
      }
    }
  }
})
```

This completes the detailed code implementation for the multi-layer print job system. Each component is designed to work together to provide a robust, automated printing solution while maintaining clear separation of concerns and comprehensive error handling.
